/* Footer Styling */
.footer {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    background: rgb(59, 49, 49);
    width: 100%;
    padding: 15px;
    position: relative; /* Changed from fixed to relative */
    bottom: 0;
    left: 0;
    margin-top: auto; /* Pushes the footer to the bottom of the page */
}

.footerContent {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo {
    width: 30px;
    height: 30px;
    object-fit: cover;
    border-radius: 5px;
}
