/* Profile Page Styles */

.pageWrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  overflow-x: hidden;
}

.pageContent {
  flex: 1;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #333;
  text-align: center;
}

.profileContainer {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.profileHeader {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.profileHeader h2 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.email {
  color: #666;
  font-size: 1rem;
}

.profileForm {
  margin-bottom: 2rem;
}

/* Profile Picture Section */
.profilePictureSection {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  gap: 2rem;
  flex-wrap: wrap;
}

.picturePreview {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid rgba(233, 142, 15, 0.8);
}

.previewImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholderInitial {
  font-size: 3rem;
  font-weight: 700;
  color: #e98e0f;
}

.pictureUpload {
  flex: 1;
  min-width: 200px;
}

.uploadButton {
  display: inline-block;
  background-color: rgba(233, 142, 15, 0.8);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
  margin-bottom: 0.5rem;
}

.uploadButton:hover {
  background-color: rgba(233, 142, 15, 1);
}

.fileInput {
  display: none;
}

.uploadHint {
  font-size: 0.875rem;
  color: #666;
  margin-top: 0.5rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.inputField {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.inputField:focus {
  outline: none;
  border-color: #9c27b0;
  box-shadow: 0 0 0 2px rgba(156, 39, 176, 0.2);
}

.formActions {
  margin-top: 2rem;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.updateButton {
  background-color: #9c27b0;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  flex: 1;
  min-width: 180px;
}

.updateButton:hover {
  background-color: #7b1fa2;
}

.updateButton:disabled {
  background-color: #d1c4e9;
  cursor: not-allowed;
}

.saveReturnButton {
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  flex: 1;
  min-width: 180px;
}

.saveReturnButton:hover {
  background-color: #388e3c;
}

.saveReturnButton:disabled {
  background-color: #c8e6c9;
  cursor: not-allowed;
}



.successMessage {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
}

.errorMessage {
  background-color: #ffebee;
  color: #c62828;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .pageContent {
    padding: 1.5rem 1rem;
  }

  .profileContainer {
    padding: 1.5rem;
  }

  .title {
    font-size: 1.75rem;
  }
}
